%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7137023482562969817
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5052264629267279113}
  - component: {fileID: 3990693672098548669}
  - component: {fileID: 7140694399045626294}
  - component: {fileID: 3004629566080465027}
  - component: {fileID: 7921911991004155457}
  m_Layer: 0
  m_Name: UI_TouchScreenInput
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5052264629267279113
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7137023482562969817}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.92999995, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3990693672098548669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7137023482562969817}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PanelSettings: {fileID: 11400000, guid: 6f80c07efbcffac4380910b5aac93c8d, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: ccd45339dc8ff154fbbe578856089eaf,
    type: 3}
  m_SortingOrder: 0
--- !u!114 &7140694399045626294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7137023482562969817}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6d7e4e4868ed5df4b928c22423c690a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MoveMagnitudeMultiplier: 1
  LookMagnitudeMultiplier: 80
  InvertLookY: 1
  MoveEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3004629566080465027}
        m_TargetAssemblyTypeName: VirtualInput, Unity.StarterAssets
        m_MethodName: VirtualMoveInput
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  LookEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3004629566080465027}
        m_TargetAssemblyTypeName: VirtualInput, Unity.StarterAssets
        m_MethodName: VirtualLookInput
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  JumpEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3004629566080465027}
        m_TargetAssemblyTypeName: VirtualInput, Unity.StarterAssets
        m_MethodName: VirtualJumpInput
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  SprintEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3004629566080465027}
        m_TargetAssemblyTypeName: VirtualInput, Unity.StarterAssets
        m_MethodName: VirtualSprintInput
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &3004629566080465027
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7137023482562969817}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed06e3b6fec5ecc4696004cedc31f0ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  StarterAssetsInputs: {fileID: 0}
--- !u!114 &7921911991004155457
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7137023482562969817}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 901182334643ba1438a25accc6bd0c79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerInput: {fileID: 0}
