fileFormatVersion: 2
guid: b86fb09770d0f2f48a47079d760e932b
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material_1
    second: {fileID: 2100000, guid: d31caadd7f747164e910fe90d74895a5, type: 2}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: texture_0
    second: {fileID: 2800000, guid: c8c66b82da8fce347910f5a0cd50e85c, type: 3}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Alert
      takeName: Alert
      internalID: -3745917278484660868
      firstFrame: 0
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Arise
      takeName: Arise
      internalID: -5175492307695106804
      firstFrame: 0
      lastFrame: 130
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Attack
      takeName: Attack
      internalID: 6720303635017113406
      firstFrame: 0
      lastFrame: 89
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: BeHit_FlyUp
      takeName: BeHit_FlyUp
      internalID: 5563359049450505430
      firstFrame: 0
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Dead
      takeName: Dead
      internalID: 8273325049105940540
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle
      takeName: Idle
      internalID: -3100369314251171874
      firstFrame: 0
      lastFrame: 55
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Running
      takeName: Running
      internalID: -9121621101649706817
      firstFrame: 0
      lastFrame: 84
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Skill_01
      takeName: Skill_01
      internalID: 8435452027487919885
      firstFrame: 0
      lastFrame: 46
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Skill_03
      takeName: Skill_03
      internalID: -7951308697055104129
      firstFrame: 0
      lastFrame: 89
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Triple_Combo_Attack
      takeName: Triple_Combo_Attack
      internalID: 4272350386825326460
      firstFrame: 0
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Unsteady_Walk
      takeName: Unsteady_Walk
      internalID: 3471970925988930994
      firstFrame: 0
      lastFrame: 19
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Walking
      takeName: Walking
      internalID: -1610863427237328480
      firstFrame: 0
      lastFrame: 32
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Character_output.fbx
      takeName: Character_output.fbx
      internalID: -2498671889216865470
      firstFrame: 0
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine02
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine01
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Meshy_Merged_Animations(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Meshy_Merged_Animations(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: 0.014807421, y: -0.03957063, z: 0.49423152}
      rotation: {x: 0.33334067, y: -0.554334, z: -0.55433387, w: 0.5237478}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000004}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: 0.022547945, y: -0.06681369, z: 0.10906315}
      rotation: {x: 0.75585926, y: -0.06869854, z: -0.65107423, w: 0.0077176522}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000005}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: -0, y: 0.18522726, z: 0}
      rotation: {x: 0.123503424, y: -0.47640786, z: -0.2754714, w: 0.82577115}
      scale: {x: 0.9999998, y: 1.0000002, z: 1.0000005}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -0.000000006556511, y: 0.2173267, z: 0.000000011324882}
      rotation: {x: -0.5319638, y: 0.26030025, z: 0.4002661, w: 0.6993178}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000005}
    - name: LeftToeBase
      parentName: LeftFoot
      position: {x: -0.000000011622905, y: 0.1789963, z: 0.000000014305114}
      rotation: {x: -0.21856225, y: 0.0723555, z: 0.016252836, w: 0.9730011}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000005}
    - name: RightUpLeg
      parentName: Hips
      position: {x: -0.02712108, y: -0.02115279, z: -0.09633652}
      rotation: {x: 0.505727, y: 0.13400808, z: -0.8291246, w: -0.19706458}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000011}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 0.0000000035762786, y: 0.17368068, z: 0.0000000023841857}
      rotation: {x: 0.19211552, y: 0.23346508, z: 0.20915374, w: 0.92996794}
      scale: {x: 1.0000002, y: 1.0000008, z: 1.0000001}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 0.000000024437904, y: 0.19636329, z: 0}
      rotation: {x: -0.60599935, y: 0.0011586248, z: -0.10885718, w: 0.78798074}
      scale: {x: 0.9999998, y: 0.9999997, z: 0.9999996}
    - name: RightToeBase
      parentName: RightFoot
      position: {x: -0.000000009536743, y: 0.14430842, z: -0.00000000834465}
      rotation: {x: -0.26533946, y: 0.014789186, z: 0.0040700226, w: 0.96403307}
      scale: {x: 0.99999994, y: 0.9999997, z: 1}
    - name: Spine02
      parentName: Hips
      position: {x: 0.004573196, y: 0.08796639, z: 0.018439468}
      rotation: {x: 0.08381423, y: 0.78123546, z: 0.06514005, w: 0.61514485}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.9999998}
    - name: Spine01
      parentName: Spine02
      position: {x: -0.000000004917383, y: 0.08999464, z: 0.000000021159648}
      rotation: {x: -0.00000001117587, y: 0.000000029802319, z: -0.000000029802319, w: 1}
      scale: {x: 0.9999998, y: 1.0000001, z: 1}
    - name: Spine
      parentName: Spine01
      position: {x: -0.0000000010430813, y: 0.0899945, z: -0.0000000080466265}
      rotation: {x: 0.02467878, y: -0.0019513953, z: 0.00036669584, w: 0.99969345}
      scale: {x: 0.9999999, y: 0.9999999, z: 0.9999996}
    - name: LeftShoulder
      parentName: Spine
      position: {x: -0.030959312, y: 0.08635039, z: -0.062029924}
      rotation: {x: 0.3681617, y: -0.49031502, z: 0.64624244, w: 0.45433334}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000005}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: -0.0000000011920929, y: 0.110686354, z: -0.00000012613833}
      rotation: {x: -0.059750766, y: -0.3688188, z: -0.15886566, w: 0.91387326}
      scale: {x: 1, y: 1.0000001, z: 1.0000002}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: -0.000000041425228, y: 0.24706665, z: -0.000000015497207}
      rotation: {x: -0.014132976, y: -0.0990146, z: -0.010698199, w: 0.9949281}
      scale: {x: 1.0000004, y: 0.9999997, z: 0.9999996}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -0.00000002592802, y: 0.16921993, z: -0.000000005960464}
      rotation: {x: 0.062346566, y: -0.09770302, z: -0.14877927, w: 0.9820548}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000002}
    - name: RightShoulder
      parentName: Spine
      position: {x: 0.031334024, y: 0.08605501, z: -0.062251654}
      rotation: {x: -0.36803204, y: -0.4902798, z: 0.6498562, w: -0.44929382}
      scale: {x: 1.000001, y: 1.0000011, z: 1.0000008}
    - name: RightArm
      parentName: RightShoulder
      position: {x: 0.0000000023841857, y: 0.110686354, z: 0.00000003680587}
      rotation: {x: 0.060583964, y: -0.37318864, z: -0.15835182, w: -0.9121319}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000004}
    - name: RightForeArm
      parentName: RightArm
      position: {x: 0.000000047683713, y: 0.24499589, z: 0.000000026226044}
      rotation: {x: -0.020315427, y: 0.025116464, z: -0.007798045, w: 0.9994477}
      scale: {x: 1.0000005, y: 1.0000006, z: 1.0000004}
    - name: RightHand
      parentName: RightForeArm
      position: {x: -0.000000007152557, y: 0.44469085, z: 0.0000000047683715}
      rotation: {x: -0.4371518, y: -0.040311754, z: -0.049531333, w: 0.8971176}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: neck
      parentName: Spine
      position: {x: -0.00037474735, y: 0.18297543, z: 0.124281384}
      rotation: {x: 0.09843709, y: -0.0012783519, z: -0.00051946734, w: 0.99514234}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000004}
    - name: Head
      parentName: neck
      position: {x: 0.00000000834465, y: 0.11747207, z: 0.000000014994294}
      rotation: {x: 0.124179125, y: -0.0004579376, z: -0.00021163224, w: 0.9922597}
      scale: {x: 1.0000002, y: 1.0000002, z: 0.9999997}
    - name: head_end
      parentName: Head
      position: {x: -0.0008559178, y: 0.15311214, z: -0.047311712}
      rotation: {x: -0.14928477, y: 0.000000004598405, z: 0.002700736, w: 0.98879063}
      scale: {x: 1, y: 1.0000002, z: 1.0000004}
    - name: headfront
      parentName: Head
      position: {x: 0.0008559286, y: 0.09979394, z: 0.047311373}
      rotation: {x: 0.21954581, y: -0.000000015541444, z: -0.00397195, w: 0.97559416}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000008}
    - name: char1
      parentName: Meshy_Merged_Animations(Clone)
      position: {x: -0.000000014422014, y: -0.0000000122961445, z: -0.00000008114848}
      rotation: {x: -0.000000014405112, y: 0.0000000037252903, z: -0.0000000037252885, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
