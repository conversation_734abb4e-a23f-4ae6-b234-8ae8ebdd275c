using UnityEngine;
using UnityEngine.AI;
using StarterAssets;

[RequireComponent(typeof(NavMeshAgent))]
public class GoblinAI : MonoBehaviour
{
    public float chaseRange = 10f;
    public float attackRange = 2f;
    public float attackDamage = 5f;
    public float attackCooldown = 0.5f;

    private ThirdPersonController _player;
    private NavMeshAgent _agent;
    private Animator _animator;
    private float _cooldown;

    private enum State { Idle, Chase, Attack }
    private State _state;

    private void Start()
    {
        _agent = GetComponent<NavMeshAgent>();
        Debug.Log(_agent);
        _animator = GetComponent<Animator>();
        _player = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
    }

    private void Update()
    {
        if (_player == null) return;

        float dist = Vector3.Distance(transform.position, _player.transform.position);

        switch (_state)
        {
            case State.Idle:
                _animator.SetFloat("Speed", 0f);
                if (!_animator.GetCurrentAnimatorStateInfo(0).IsName("Idle Walk Run Blend"))
                    _animator.Play("Idle Walk Run Blend");
                if (dist <= chaseRange)
                {
                    _state = State.Chase;
                }
                break;

            case State.Chase:
                // Set Speed parameter based on agent velocity magnitude for blend tree
                _animator.SetFloat("Speed", _agent.velocity.magnitude);
                if (!_animator.GetCurrentAnimatorStateInfo(0).IsName("Idle Walk Run Blend"))
                    _animator.Play("Idle Walk Run Blend");
                if (dist > chaseRange)
                {
                    _state = State.Idle;
                    if (_agent.isOnNavMesh) _agent.ResetPath();
                }
                else if (dist <= attackRange)
                {
                    _state = State.Attack;
                    if (_agent.isOnNavMesh) _agent.ResetPath();
                    _cooldown = 0f;
                }
                else
                {
                    if (_agent.isOnNavMesh) _agent.SetDestination(_player.transform.position);
                }
                break;

            case State.Attack:
                // Optionally, set Speed to a value that blends to attack/run in your blend tree
                _animator.SetFloat("Speed", _agent.velocity.magnitude); // Or set to a fixed value if needed
                transform.LookAt(_player.transform.position);
                if (_cooldown <= 0f)
                {
                    PerformAttack();
                    if (!_animator.GetCurrentAnimatorStateInfo(0).IsName("Idle Walk Run Blend"))
                        _animator.Play("Idle Walk Run Blend");
                    _cooldown = attackCooldown;
                }
                else
                {
                    _cooldown -= Time.deltaTime;
                    if (_cooldown <= 0f)
                    {
                        _state = dist <= attackRange ? State.Attack : State.Chase;
                    }
                }
                break;
        }
    }

    private void PerformAttack()
    {

        if (_animator != null)
            _animator.SetTrigger("AttackTrigger");

        var dmg = _player.GetComponent<Damageable>();
        if (dmg != null)
            dmg.ApplyDamage(attackDamage);
    }
}
